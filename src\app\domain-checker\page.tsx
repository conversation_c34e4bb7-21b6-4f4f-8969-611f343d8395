'use client'

import React, { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card'
import { Button } from '../../components/ui/button'
import { Badge } from '../../components/ui/badge'
import { Textarea } from '../../components/ui/textarea'
import { Progress } from '../../components/ui/progress'
import { 
  Globe, 
  Search, 
  CheckCircle, 
  XCircle, 
  AlertCircle, 
  ExternalLink, 
  Copy, 
  Download,
  Loader2,
  Info
} from 'lucide-react'
import FrontendHeader from '../../components/frontend-header'
import FrontendFooter from '../../components/frontend-footer'
import { toast } from 'sonner'

interface DomainResult {
  domain: string
  registered: boolean | null
  status: string
  message: string
}

interface BatchCheckResult {
  checking: boolean
  progress: number
  results: DomainResult[]
  stats: {
    total: number
    available: number
    registered: number
    failed: number
  } | null
}

export default function DomainCheckerPage() {
  const [keywords, setKeywords] = useState('')
  const [batchCheck, set<PERSON>atch<PERSON>he<PERSON>] = useState<BatchCheckResult>({
    checking: false,
    progress: 0,
    results: [],
    stats: null
  })

  // 处理关键词，生成域名列表
  const processKeywords = (input: string): string[] => {
    if (!input.trim()) return []

    // 按行分割，每行作为一个完整的关键词组合
    const lines = input.split('\n').filter(line => line.trim())
    const domains: string[] = []

    lines.forEach(line => {
      // 清理关键词：移除特殊字符，转小写，移除空格
      const cleanKeyword = line.trim().toLowerCase()
        .replace(/[^a-z0-9\s]/g, '')  // 移除特殊字符，保留字母数字和空格
        .replace(/\s+/g, '')          // 移除所有空格

      if (cleanKeyword && cleanKeyword.length > 0) {
        const domain = `${cleanKeyword}.com`
        if (!domains.includes(domain)) {
          domains.push(domain)
        }
      }
    })

    return domains
  }

  // 批量检查域名
  const checkDomains = async () => {
    const domains = processKeywords(keywords)
    
    if (domains.length === 0) {
      toast.error('请输入有效的关键词')
      return
    }
    
    if (domains.length > 50) {
      toast.error('一次最多检查50个域名')
      return
    }

    setBatchCheck({
      checking: true,
      progress: 0,
      results: [],
      stats: null
    })

    try {
      const response = await fetch('/api/domain/batch-check', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ domains })
      })

      const data = await response.json()

      if (data.success && data.data) {
        setBatchCheck({
          checking: false,
          progress: 100,
          results: data.data.results,
          stats: data.data.stats
        })
        toast.success(`检查完成！共检查${data.data.stats.total}个域名`)
      } else {
        setBatchCheck({
          checking: false,
          progress: 0,
          results: [],
          stats: null
        })
        toast.error('批量检查失败，请稍后重试')
      }
    } catch (error) {
      setBatchCheck({
        checking: false,
        progress: 0,
        results: [],
        stats: null
      })
      toast.error('检查失败，请稍后重试')
    }
  }

  // 获取状态颜色和图标
  const getStatusDisplay = (result: DomainResult) => {
    if (result.registered === false) {
      return {
        icon: <CheckCircle className="h-4 w-4 text-green-500" />,
        badge: <Badge className="bg-green-100 text-green-800">可能可用</Badge>,
        color: 'text-green-600'
      }
    } else if (result.registered === true) {
      return {
        icon: <XCircle className="h-4 w-4 text-red-500" />,
        badge: <Badge className="bg-red-100 text-red-800">已注册</Badge>,
        color: 'text-red-600'
      }
    } else {
      return {
        icon: <AlertCircle className="h-4 w-4 text-yellow-500" />,
        badge: <Badge className="bg-yellow-100 text-yellow-800">查询失败</Badge>,
        color: 'text-yellow-600'
      }
    }
  }

  // 复制域名列表
  const copyDomains = (type: 'all' | 'available') => {
    let domainsToCopy: string[] = []
    
    if (type === 'all') {
      domainsToCopy = batchCheck.results.map(r => r.domain)
    } else {
      domainsToCopy = batchCheck.results
        .filter(r => r.registered === false)
        .map(r => r.domain)
    }
    
    if (domainsToCopy.length === 0) {
      toast.error('没有可复制的域名')
      return
    }
    
    navigator.clipboard.writeText(domainsToCopy.join('\n'))
    toast.success(`已复制${domainsToCopy.length}个域名到剪贴板`)
  }

  // 导出结果
  const exportResults = () => {
    if (batchCheck.results.length === 0) {
      toast.error('没有可导出的结果')
      return
    }
    
    const csvContent = [
      '域名,状态,消息',
      ...batchCheck.results.map(r => 
        `${r.domain},${r.registered === false ? '可能可用' : r.registered === true ? '已注册' : '查询失败'},${r.message}`
      )
    ].join('\n')
    
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `域名检查结果_${new Date().toISOString().split('T')[0]}.csv`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    
    toast.success('结果已导出为CSV文件')
  }

  return (
    <div className="min-h-screen bg-background">
      <FrontendHeader />
      
      {/* 页面标题 */}
      <section className="py-12 px-4 bg-gradient-to-r from-blue-50 via-purple-50 to-pink-50 border-b">
        <div className="container mx-auto text-center">
          <div className="flex items-center justify-center mb-6">
            <div className="p-4 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl mr-4">
              <Globe className="h-10 w-10 text-white" />
            </div>
            <h1 className="text-5xl font-bold text-gray-900">域名检查工具</h1>
          </div>
          <p className="text-2xl text-gray-700 max-w-4xl mx-auto mb-8">
            输入关键词，自动生成.com域名并检查注册状态，支持批量检测，一键跳转阿里云查询实际可用性
          </p>
          <div className="flex flex-wrap justify-center gap-4 mb-8">
            <Badge className="bg-blue-100 text-blue-800 px-4 py-2 text-lg">批量检测</Badge>
            <Badge className="bg-green-100 text-green-800 px-4 py-2 text-lg">实时查询</Badge>
            <Badge className="bg-purple-100 text-purple-800 px-4 py-2 text-lg">阿里云跳转</Badge>
            <Badge className="bg-orange-100 text-orange-800 px-4 py-2 text-lg">结果导出</Badge>
          </div>
        </div>
      </section>

      {/* 主要内容 */}
      <section className="py-12 px-4">
        <div className="container mx-auto max-w-6xl">
          
          {/* 输入区域 */}
          <Card className="mb-8">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Search className="h-6 w-6 mr-2 text-blue-600" />
                关键词输入
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <Textarea
                    placeholder="请输入关键词，每行一个关键词组合：&#10;&#10;示例：&#10;Summer Bag&#10;Summer Backpack&#10;Travel Backpack&#10;&#10;将自动转换为：&#10;summerbag.com&#10;summerbackpack.com&#10;travelbackpack.com"
                    value={keywords}
                    onChange={(e) => setKeywords(e.target.value)}
                    className="min-h-[200px] resize-none"
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <Button 
                      onClick={checkDomains}
                      disabled={batchCheck.checking || !keywords.trim()}
                      className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700"
                    >
                      {batchCheck.checking ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          检查中...
                        </>
                      ) : (
                        <>
                          <Search className="h-4 w-4 mr-2" />
                          开始检查
                        </>
                      )}
                    </Button>
                    
                    {processKeywords(keywords).length > 0 && (
                      <span className="text-sm text-gray-600">
                        将检查 {processKeywords(keywords).length} 个域名
                      </span>
                    )}
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Info className="h-4 w-4 text-blue-500" />
                    <span className="text-sm text-gray-600">最多支持50个域名</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 检查进度 */}
          {batchCheck.checking && (
            <Card className="mb-8">
              <CardContent className="pt-6">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">检查进度</span>
                    <span className="text-sm text-gray-600">正在检查域名...</span>
                  </div>
                  <Progress value={batchCheck.progress} className="w-full" />
                </div>
              </CardContent>
            </Card>
          )}

          {/* 检查结果统计 */}
          {batchCheck.stats && (
            <Card className="mb-8">
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span className="flex items-center">
                    <CheckCircle className="h-6 w-6 mr-2 text-green-600" />
                    检查结果统计
                  </span>
                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => copyDomains('available')}
                    >
                      <Copy className="h-4 w-4 mr-1" />
                      复制可用域名
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => copyDomains('all')}
                    >
                      <Copy className="h-4 w-4 mr-1" />
                      复制全部
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={exportResults}
                    >
                      <Download className="h-4 w-4 mr-1" />
                      导出CSV
                    </Button>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center p-4 bg-gray-50 rounded-lg">
                    <div className="text-2xl font-bold text-gray-900">{batchCheck.stats.total}</div>
                    <div className="text-sm text-gray-600">总计</div>
                  </div>
                  <div className="text-center p-4 bg-green-50 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">{batchCheck.stats.available}</div>
                    <div className="text-sm text-gray-600">可能可用</div>
                  </div>
                  <div className="text-center p-4 bg-red-50 rounded-lg">
                    <div className="text-2xl font-bold text-red-600">{batchCheck.stats.registered}</div>
                    <div className="text-sm text-gray-600">已注册</div>
                  </div>
                  <div className="text-center p-4 bg-yellow-50 rounded-lg">
                    <div className="text-2xl font-bold text-yellow-600">{batchCheck.stats.failed}</div>
                    <div className="text-sm text-gray-600">查询失败</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* 详细结果 */}
          {batchCheck.results.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Globe className="h-6 w-6 mr-2 text-blue-600" />
                  域名检查详情
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {batchCheck.results.map((result, index) => {
                    const statusDisplay = getStatusDisplay(result)
                    const cleanDomain = result.domain.replace('.com', '')
                    
                    return (
                      <div key={index} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50">
                        <div className="flex items-center space-x-3">
                          {statusDisplay.icon}
                          <div>
                            <div className="font-medium text-gray-900">{result.domain}</div>
                            <div className="text-sm text-gray-600">{result.message}</div>
                          </div>
                        </div>
                        
                        <div className="flex items-center space-x-3">
                          {statusDisplay.badge}
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => window.open(`https://wanwang.aliyun.com/domain/searchresult/?keyword=${encodeURIComponent(cleanDomain)}`, '_blank')}
                          >
                            <ExternalLink className="h-4 w-4 mr-1" />
                            阿里云查询
                          </Button>
                        </div>
                      </div>
                    )
                  })}
                </div>
              </CardContent>
            </Card>
          )}

          {/* 使用说明 */}
          {batchCheck.results.length === 0 && !batchCheck.checking && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mt-12">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Info className="h-6 w-6 mr-2 text-blue-600" />
                    使用说明
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="p-4 bg-blue-50 rounded-lg">
                      <h4 className="font-semibold text-blue-800 mb-2">1. 输入关键词</h4>
                      <p className="text-sm text-gray-700">支持多种格式：每行一个、逗号分隔、空格分隔</p>
                    </div>
                    <div className="p-4 bg-green-50 rounded-lg">
                      <h4 className="font-semibold text-green-800 mb-2">2. 自动生成域名</h4>
                      <p className="text-sm text-gray-700">系统会自动清理关键词并生成.com域名</p>
                    </div>
                    <div className="p-4 bg-purple-50 rounded-lg">
                      <h4 className="font-semibold text-purple-800 mb-2">3. 批量检查状态</h4>
                      <p className="text-sm text-gray-700">一次性检查多个域名的注册状态</p>
                    </div>
                    <div className="p-4 bg-orange-50 rounded-lg">
                      <h4 className="font-semibold text-orange-800 mb-2">4. 阿里云验证</h4>
                      <p className="text-sm text-gray-700">点击按钮跳转阿里云查询实际可用性</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <CheckCircle className="h-6 w-6 mr-2 text-green-600" />
                    功能特色
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex items-center">
                      <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                      <span className="text-sm">支持批量检查，最多50个域名</span>
                    </div>
                    <div className="flex items-center">
                      <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                      <span className="text-sm">智能关键词处理，自动去除特殊字符</span>
                    </div>
                    <div className="flex items-center">
                      <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                      <span className="text-sm">实时DNS查询，准确判断注册状态</span>
                    </div>
                    <div className="flex items-center">
                      <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                      <span className="text-sm">一键复制可用域名列表</span>
                    </div>
                    <div className="flex items-center">
                      <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                      <span className="text-sm">结果导出CSV格式</span>
                    </div>
                    <div className="flex items-center">
                      <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                      <span className="text-sm">直接跳转阿里云域名注册</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* 示例展示 */}
          {batchCheck.results.length === 0 && !batchCheck.checking && (
            <Card className="mt-8">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Search className="h-6 w-6 mr-2 text-purple-600" />
                  输入示例
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="p-4 bg-gray-50 rounded-lg">
                    <h4 className="font-semibold text-gray-800 mb-2">每行一个关键词</h4>
                    <div className="text-sm text-gray-600 font-mono bg-white p-3 rounded border">
                      cleaning cloth<br/>
                      kitchen towel<br/>
                      dish cloth<br/>
                      microfiber cleaning
                    </div>
                  </div>

                  <div className="p-4 bg-gray-50 rounded-lg">
                    <h4 className="font-semibold text-gray-800 mb-2">逗号分隔</h4>
                    <div className="text-sm text-gray-600 font-mono bg-white p-3 rounded border">
                      cleaning cloth, kitchen towel, dish cloth, microfiber cleaning
                    </div>
                  </div>

                  <div className="p-4 bg-gray-50 rounded-lg">
                    <h4 className="font-semibold text-gray-800 mb-2">混合格式</h4>
                    <div className="text-sm text-gray-600 font-mono bg-white p-3 rounded border">
                      cleaning cloth<br/>
                      kitchen towel, dish cloth<br/>
                      microfiber cleaning
                    </div>
                  </div>
                </div>

                <div className="mt-6 p-4 bg-yellow-50 rounded-lg border border-yellow-200">
                  <div className="flex items-start">
                    <AlertCircle className="h-5 w-5 text-yellow-600 mr-2 mt-0.5" />
                    <div>
                      <h4 className="font-semibold text-yellow-800 mb-1">注意事项</h4>
                      <ul className="text-sm text-yellow-700 space-y-1">
                        <li>• 每行输入一个完整的关键词组合，不支持空格分割单词</li>
                        <li>• 系统会自动转换为小写并移除空格和特殊字符</li>
                        <li>• 只生成.com域名进行检查</li>
                        <li>• DNS查询结果仅供参考，实际可用性请以阿里云查询为准</li>
                        <li>• 建议在阿里云确认域名可用性后再进行注册</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

        </div>
      </section>

      <FrontendFooter />
    </div>
  )
}
